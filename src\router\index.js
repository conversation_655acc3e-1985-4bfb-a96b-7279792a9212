import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      // path: '/dashboard',
      name: 'dashboard',
      // 性能数据分析平台页面
      // 使用懒加载方式导入dashboard组件
      component: () => import('../views/dashboard/index.vue'),
    },
    {
      path: '/gpu-memory',
      name: 'gpu-memory',
      // GPU显存分析页面
      // 使用懒加载方式导入gpu-memory组件
      component: () => import('../views/gpu-memory/index.vue'),
    },
  ],
})

export default router
