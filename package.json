{"name": "bechmark-front", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.2", "axios": "^1.12.2", "echarts": "^6.0.0", "element-plus": "^2.11.3", "pinia": "^3.0.3", "vue": "^3.5.18", "vue-demi": "^0.14.10", "vue-router": "^4.5.1"}, "devDependencies": {"@eslint/js": "^9.31.0", "@vitejs/plugin-vue": "^6.0.1", "@vue/eslint-config-prettier": "^10.2.0", "eslint": "^9.31.0", "eslint-plugin-vue": "~10.3.0", "globals": "^16.3.0", "js-cookie": "^3.0.5", "prettier": "3.6.2", "qs": "^6.14.0", "sass": "^1.93.2", "unplugin-auto-import": "^20.1.0", "unplugin-vue-components": "^29.1.0", "vite": "^7.0.6", "vite-plugin-compression": "^0.5.1", "vite-plugin-vue-devtools": "^8.0.0", "vite-plugin-vue-setup-extend-plus": "^0.1.0"}}