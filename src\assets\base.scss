// SCSS Variables - Color palette from https://github.com/vuejs/theme
$vt-c-white: #ffffff;
$vt-c-white-soft: #f8f8f8;
$vt-c-white-mute: #f2f2f2;

$vt-c-black: #181818;
$vt-c-black-soft: #222222;
$vt-c-black-mute: #282828;

$vt-c-indigo: #2c3e50;

$vt-c-divider-light-1: rgba(60, 60, 60, 0.29);
$vt-c-divider-light-2: rgba(60, 60, 60, 0.12);
$vt-c-divider-dark-1: rgba(84, 84, 84, 0.65);
$vt-c-divider-dark-2: rgba(84, 84, 84, 0.48);

$vt-c-text-light-1: $vt-c-indigo;
$vt-c-text-light-2: rgba(60, 60, 60, 0.66);
$vt-c-text-dark-1: $vt-c-white;
$vt-c-text-dark-2: rgba(235, 235, 235, 0.64);

// Semantic color variables
$color-background: $vt-c-white;
$color-background-soft: $vt-c-white-soft;
$color-background-mute: $vt-c-white-mute;

$color-border: $vt-c-divider-light-2;
$color-border-hover: $vt-c-divider-light-1;

$color-heading: $vt-c-text-light-1;
$color-text: $vt-c-text-light-1;

$section-gap: 160px;

// Dark theme colors
$color-background-dark: $vt-c-black;
$color-background-soft-dark: $vt-c-black-soft;
$color-background-mute-dark: $vt-c-black-mute;

$color-border-dark: $vt-c-divider-dark-2;
$color-border-hover-dark: $vt-c-divider-dark-1;

$color-heading-dark: $vt-c-text-dark-1;
$color-text-dark: $vt-c-text-dark-2;

// CSS Custom Properties for runtime theme switching
:root {
  --vt-c-white: #{$vt-c-white};
  --vt-c-white-soft: #{$vt-c-white-soft};
  --vt-c-white-mute: #{$vt-c-white-mute};

  --vt-c-black: #{$vt-c-black};
  --vt-c-black-soft: #{$vt-c-black-soft};
  --vt-c-black-mute: #{$vt-c-black-mute};

  --vt-c-indigo: #{$vt-c-indigo};

  --vt-c-divider-light-1: #{$vt-c-divider-light-1};
  --vt-c-divider-light-2: #{$vt-c-divider-light-2};
  --vt-c-divider-dark-1: #{$vt-c-divider-dark-1};
  --vt-c-divider-dark-2: #{$vt-c-divider-dark-2};

  --vt-c-text-light-1: #{$vt-c-text-light-1};
  --vt-c-text-light-2: #{$vt-c-text-light-2};
  --vt-c-text-dark-1: #{$vt-c-text-dark-1};
  --vt-c-text-dark-2: #{$vt-c-text-dark-2};

  // Semantic color variables for this project
  --color-background: #{$color-background};
  --color-background-soft: #{$color-background-soft};
  --color-background-mute: #{$color-background-mute};

  --color-border: #{$color-border};
  --color-border-hover: #{$color-border-hover};

  --color-heading: #{$color-heading};
  --color-text: #{$color-text};

  --section-gap: #{$section-gap};

  @media (prefers-color-scheme: dark) {
    --color-background: #{$color-background-dark};
    --color-background-soft: #{$color-background-soft-dark};
    --color-background-mute: #{$color-background-mute-dark};

    --color-border: #{$color-border-dark};
    --color-border-hover: #{$color-border-hover-dark};

    --color-heading: #{$color-heading-dark};
    --color-text: #{$color-text-dark};
  }
}

// Base styles
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  font-weight: normal;
}

body {
  min-height: 100vh;
  color: var(--color-text);
  background: var(--color-background);
  transition: color 0.5s, background-color 0.5s;
  line-height: 1.6;
  font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  font-size: 15px;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
