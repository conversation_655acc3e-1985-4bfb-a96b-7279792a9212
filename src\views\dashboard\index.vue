<template>
  <div class="dashboard-container">
    <!-- 页面标题 -->
    <h1 class="page-title">ECO - 性能数据分析平台</h1>

    <!-- 控制面板 -->
    <div class="control-panel">
      <!-- 第一行：日期、配置和指标选择 -->
      <div class="control-row">
        <!-- 日期选择 -->
        <div class="control-item" style="width: 20%">
          <label class="control-label">选择日期：</label>
          <el-select
            v-model="selectedDate"
            placeholder="请选择日期"
            @change="onDateChange"
            style="width: 100%"
          >
            <el-option
              v-for="date in availableDates"
              :key="date.value"
              :label="date.label"
              :value="date.value"
            />
          </el-select>
        </div>

        <!-- 配置选择 -->
        <div class="control-item" style="width: 25%">
          <label class="control-label">选择机器配置：</label>
          <el-select
            v-model="selectedConfigs"
            multiple
            placeholder="请选择配置..."
            @change="onConfigChange"
            style="width: 100%"
          >
            <el-option
              v-for="config in availableConfigs"
              :key="config.value"
              :label="config.label"
              :value="config.value"
            />
          </el-select>
        </div>

        <!-- 指标选择 -->
        <div class="control-item" style="width: 20%">
          <label class="control-label">选择性能指标：</label>
          <el-select
            v-model="selectedMetric"
            placeholder="请选择指标..."
            @change="onMetricChange"
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="metric in availableMetrics"
              :key="metric.value"
              :label="metric.label"
              :value="metric.value"
            />
          </el-select>
        </div>

        <!-- GPU显存按钮 -->
        <div class="control-item" style="width: 10%">
          <el-button
            type="primary"
            text
            @click="goToGPUMemoryPage"
            style="width: 100%; height: 38px; font-size: 14px"
          >
            查看GPU显存
          </el-button>
        </div>
      </div>

      <!-- 第二行：对比日期选择 -->
      <div class="control-row">
        <div class="control-item" style="width: 25%">
          <label class="control-label">对比日期：</label>
          <el-select
            v-model="compareDate"
            placeholder="选择对比日期"
            clearable
            :disabled="!enableCompare"
            style="width: 100%"
          >
            <el-option
              v-for="date in availableDates"
              :key="date.value"
              :label="date.label"
              :value="date.value"
            />
          </el-select>
          <el-checkbox v-model="enableCompare" style="margin-left: 10px"> 启用对比 </el-checkbox>
        </div>
      </div>

      <!-- 第三行：字段选择 -->
      <div class="control-row">
        <div class="control-item" style="width: 100%">
          <label class="control-label">选择字段：</label>
          <!-- 字段选择头部：包含全选/清空按钮 -->
          <div class="field-header">
            <span class="field-title"
              >字段选择 ({{ selectedFields.length }}/{{ availableFields.length }})</span
            >
            <div class="field-actions">
              <el-button size="small" @click="selectAllFields" style="margin-right: 10px">
                全选
              </el-button>
              <el-button size="small" @click="clearAllFields"> 清空 </el-button>
            </div>
          </div>
          <div class="field-toggle-container">
            <div class="field-grid">
              <el-checkbox-group v-model="selectedFields">
                <el-checkbox
                  v-for="field in availableFields"
                  :key="field"
                  :value="field"
                  class="field-checkbox"
                  :title="field"
                >
                  {{ field }}
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </div>
      </div>

      <!-- 更新按钮 -->
      <div class="update-section">
        <el-button type="primary" size="large" @click="updateChart" :loading="loading">
          更新图表
        </el-button>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="chart-section">
      <div ref="chartContainer" class="chart-container"></div>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <h4 style="margin-top: 30px; margin-bottom: 15px">性能指标统计</h4>
      <el-table
        :data="tableData"
        style="width: 100%"
        v-loading="loading"
        :page-size="30"
        stripe
        border
        :default-sort="{ prop: '平均值', order: 'descending' }"
      >
        <el-table-column prop="配置" label="配置" sortable show-overflow-tooltip />
        <el-table-column prop="字段" label="字段" sortable show-overflow-tooltip />
        <el-table-column
          prop="平均值"
          label="平均值"
          sortable
          :sort-method="(a, b) => a.平均值 - b.平均值"
        >
          <template #default="scope">
            {{
              typeof scope.row.平均值 === 'number' ? scope.row.平均值.toFixed(2) : scope.row.平均值
            }}
          </template>
        </el-table-column>
        <el-table-column
          prop="最小值"
          label="最小值"
          sortable
          :sort-method="(a, b) => a.最小值 - b.最小值"
        >
          <template #default="scope">
            {{
              typeof scope.row.最小值 === 'number' ? scope.row.最小值.toFixed(2) : scope.row.最小值
            }}
          </template>
        </el-table-column>
        <el-table-column
          prop="最大值"
          label="最大值"
          sortable
          :sort-method="(a, b) => a.最大值 - b.最大值"
        >
          <template #default="scope">
            {{
              typeof scope.row.最大值 === 'number' ? scope.row.最大值.toFixed(2) : scope.row.最大值
            }}
          </template>
        </el-table-column>
        <el-table-column
          prop="标准差"
          label="标准差"
          sortable
          :sort-method="(a, b) => a.标准差 - b.标准差"
        >
          <template #default="scope">
            {{
              typeof scope.row.标准差 === 'number' ? scope.row.标准差.toFixed(2) : scope.row.标准差
            }}
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup name="Dashboard">
import { ref, onMounted, nextTick, watch } from 'vue'
import { useRouter } from 'vue-router'
import * as echarts from 'echarts'
import { ElMessage } from 'element-plus'
import { calculateStatistics, getGPUMemoryData } from '../../services/dataService.js'
import { useDashboardApi } from '../../api/dashboaard/index.js'
import { chartDataMock } from '../../views/dashboard/components/mockData.js'
const { getDate, getDeviceConfig, getAvailableMetrics, getAvailableFields, getChartData } =
  useDashboardApi()
const router = useRouter()

// 选择的数据
const selectedDate = ref('')
const selectedConfigs = ref([])
const selectedMetric = ref('')
const compareDate = ref('')
const enableCompare = ref(false)
const selectedFields = ref([])
const loading = ref(false)

// 可选项数据
const availableDates = ref([])
const availableConfigs = ref([])
const availableMetrics = ref([])
const availableFields = ref([])

// 图表和表格数据
const chartContainer = ref(null)
const tableData = ref([])
let chartInstance = null
// 当前性能数据
const currentPerformanceData = ref({})

// 初始化数据
const initializeData = async () => {
  try {
    loading.value = true

    // 获取可用日期 - 使用真实API
    const dateResponse = await getDate()
    if (dateResponse && dateResponse.data) {
      availableDates.value = dateResponse.data.map((date) => ({
        label: date,
        value: date,
      }))
    }

    // 设置默认值
    if (availableDates.value.length > 0) {
      selectedDate.value = availableDates.value[0].value
      await loadConfigsForDate(selectedDate.value)
    }

    // 在有日期和配置后，加载指标
    if (selectedDate.value && selectedConfigs.value.length > 0) {
      await loadMetricsForDateAndConfigs()
    }
  } catch (error) {
    ElMessage.error('初始化数据失败!')
  } finally {
    loading.value = false
  }
}

// 加载指定日期的配置 - 使用真实API
const loadConfigsForDate = async (date) => {
  try {
    const response = await getDeviceConfig({ date })
    if (response && response.data) {
      availableConfigs.value = response.data?.configs?.map((config) => ({
        label: config,
        value: config,
      }))
    }

    // 清空之前的配置选择
    selectedConfigs.value = []

    // 默认选择第一个配置
    if (availableConfigs.value.length > 0) {
      selectedConfigs.value = [availableConfigs.value[0].value]
    }

    // 配置加载完成后，重新加载指标
    if (date && selectedConfigs.value.length > 0) {
      await loadMetricsForDateAndConfigs()
    }
  } catch (error) {
    ElMessage.error('加载配置失败: ' + error.message)
  }
}

// 加载指定日期和配置的可用指标
const loadMetricsForDateAndConfigs = async () => {
  if (!selectedDate.value || selectedConfigs.value.length === 0) {
    return
  }

  try {
    const response = await getAvailableMetrics({
      date: selectedDate.value,
      configs: selectedConfigs.value.join(','),
    })

    if (response && response.data) {
      availableMetrics.value = response.data.map((metric) => ({
        label: metric,
        value: metric,
      }))
    }

    // 设置默认指标
    if (availableMetrics.value.length > 0 && !selectedMetric.value) {
      // 优先选择'GPU'指标，如果不存在则使用第一个可用指标
      const gpuMetric = availableMetrics.value.find((metric) => metric.value === 'GPU')
      selectedMetric.value = gpuMetric ? gpuMetric.value : availableMetrics.value[0].value
    }
  } catch (error) {
    ElMessage.error('加载指标失败: ' + error.message)
    availableMetrics.value = []
  }
}

// 加载指定指标的字段
const loadFieldsForMetric = async () => {
  if (!selectedDate.value || selectedConfigs.value.length === 0 || !selectedMetric.value) {
    return
  }

  try {
    // 获取字段列表
    const response = await getAvailableFields({
      date: selectedDate.value,
      configs: selectedConfigs.value.join(','), // 多个配置用逗号隔开
      metric: selectedMetric.value,
    })

    if (response && response.data) {
      availableFields.value = response.data
    }
  } catch (error) {
    ElMessage.error('加载字段失败: ' + error.message)
    availableFields.value = []
  }
}

// 事件处理函数
const onDateChange = async (value) => {
  console.log('Date changed:', value)
  await loadConfigsForDate(value)
  // 重新加载字段
  if (selectedMetric.value) {
    await loadFieldsForMetric()
  }
}

const onConfigChange = async (value) => {
  console.log('Config changed:', value)
  // 配置变化时重新加载指标
  await loadMetricsForDateAndConfigs()
  // 重新加载字段
  if (selectedMetric.value) {
    await loadFieldsForMetric()
  }
}

const onMetricChange = async (value) => {
  console.log('Metric changed:', value)
  // 清空之前选择的字段
  selectedFields.value = []
  await loadFieldsForMetric()
}

const goToGPUMemoryPage = () => {
  router.push('/gpu-memory')
}

// 全选字段
const selectAllFields = () => {
  selectedFields.value = [...availableFields.value]
  ElMessage.success('已全选所有字段')
}

// 清空字段选择
const clearAllFields = () => {
  selectedFields.value = []
  ElMessage.success('已清空字段选择')
}

const updateChart = async () => {
  if (!selectedDate.value || selectedConfigs.value.length === 0 || !selectedMetric.value) {
    ElMessage.warning('请选择日期、配置和指标')
    return
  }
  if (selectedFields.value.length === 0) {
    ElMessage.warning('请至少选择一个字段')
    return
  }
  loading.value = true
  try {
    // 准备API参数
    const apiParams = {
      date: selectedDate.value,
      configs: selectedConfigs.value,
      metric: selectedMetric.value,
      fields: selectedFields.value,
      is_compare: enableCompare.value && compareDate.value !== null,
      ...(enableCompare.value && compareDate.value && { compare_date: compareDate.value }),
    }

    console.log('发送图表数据请求:', apiParams)
    // 使用getChartData API获取图表数据
    const response = await getChartData(apiParams)
    console.log('图表数据API响应:', response)
    if (response.code == 200 && response.data) {
      // 验证返回数据的结构
      if (typeof response.data === 'object' && Object.keys(response.data).length > 0) {
        currentPerformanceData.value = response.data
        // 更新图表
        updateChartDisplay()
        // 更新表格
        updateTableDisplay()
        ElMessage.success('图表更新成功')
      } else {
        throw new Error('API返回的数据为空或格式不正确')
      }
    } else {
      throw new Error('API返回数据格式错误')
    }
  } catch (error) {
    console.error('图表数据加载失败:', error)
    ElMessage.error(error.message || '图表数据加载失败，请重试')

    // 清空当前数据，避免显示过期数据
    currentPerformanceData.value = {}

    // 重置图表显示
    if (chartInstance) {
      const emptyOption = {
        title: {
          text: '数据加载失败，请重试',
          left: 'center',
          top: 'middle',
          textStyle: {
            color: '#ff4d4f',
            fontSize: 16,
          },
        },
      }
      chartInstance.setOption(emptyOption, true)
    }

    // 清空表格数据
    tableData.value = []
  } finally {
    loading.value = false
  }
}

// 更新图表显示
const updateChartDisplay = () => {
  if (!chartInstance || !currentPerformanceData.value) return
  updateChartWithNewFormat()

  // 检查是否是新的chartDataMock格式
  // if (currentPerformanceData.value.x_axis && currentPerformanceData.value.series) {
  //   updateChartWithNewFormat()
  //   return
  // }

  // // 原有的数据格式处理逻辑（保持兼容性）
  // if (Object.keys(currentPerformanceData.value).length === 0) return

  // // 生成图表数据 - 支持多字段和多配置的组合
  // const seriesData = []
  // const legendData = []

  // Object.entries(currentPerformanceData.value).forEach(([config, configData]) => {
  //   selectedFields.value.forEach((field) => {
  //     if (configData[field] && Array.isArray(configData[field])) {
  //       const seriesName = `${field} (${config})`
  //       legendData.push(seriesName)

  //       // 将数据转换为时间序列格式 [时间, 值]
  //       const data = configData[field].map((value, index) => [
  //         index * 0.1, // 时间间隔0.1秒
  //         value,
  //       ])

  //       seriesData.push({
  //         name: seriesName,
  //         type: 'line',
  //         data: data,
  //         smooth: true,
  //         symbol: 'none', // 移除数据点圆圈
  //         lineStyle: {
  //           width: 1.5, // 减少线宽，使线条更细
  //           type:
  //             Object.keys(currentPerformanceData.value).indexOf(config) === 0 ? 'solid' : 'dashed',
  //         },
  //         emphasis: {
  //           focus: 'series',
  //           lineStyle: {
  //             width: 2, // 鼠标悬停时稍微加粗
  //           },
  //         },
  //       })
  //     }
  //   })
  // })

  // const option = {
  //   title: {
  //     text: `${selectedMetric.value} - 性能指标对比`,
  //     left: 'center',
  //     textStyle: {
  //       fontSize: 16,
  //       fontWeight: 'bold',
  //     },
  //   },
  //   tooltip: {
  //     trigger: 'axis',
  //     axisPointer: {
  //       type: 'cross',
  //       lineStyle: {
  //         color: '#999',
  //         width: 1,
  //         type: 'dashed',
  //       },
  //     },
  //     backgroundColor: 'rgba(255, 255, 255, 0.95)',
  //     borderColor: '#ddd',
  //     borderWidth: 1,
  //     borderRadius: 6,
  //     textStyle: {
  //       color: '#333',
  //       fontSize: 12,
  //     },
  //     formatter: function (params) {
  //       let result = `<div style="font-weight: bold; margin-bottom: 5px;">时间: ${params[0].data[0].toFixed(2)}秒</div>`
  //       params.forEach((param) => {
  //         result += `<div style="margin: 2px 0;"><span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 5px;"></span>${param.seriesName}: ${param.data[1].toFixed(2)}</div>`
  //       })
  //       return result
  //     },
  //   },
  //   legend: {
  //     data: legendData,
  //     top: 40,
  //     type: 'scroll',
  //     orient: 'horizontal',
  //     textStyle: {
  //       fontSize: 12,
  //       color: '#666',
  //     },
  //     itemGap: 20,
  //     itemWidth: 25,
  //     itemHeight: 14,
  //     icon: 'line',
  //   },
  //   grid: {
  //     left: 60,
  //     right: 40,
  //     top: 90,
  //     bottom: 60,
  //     containLabel: true,
  //   },
  //   xAxis: {
  //     type: 'value',
  //     name: '时间 (秒)',
  //     nameLocation: 'middle',
  //     nameGap: 30,
  //     nameTextStyle: {
  //       color: '#666',
  //       fontSize: 12,
  //     },
  //     axisLabel: {
  //       formatter: '{value}s',
  //       color: '#666',
  //     },
  //     axisLine: {
  //       lineStyle: {
  //         color: '#ddd',
  //       },
  //     },
  //     splitLine: {
  //       show: true,
  //       lineStyle: {
  //         color: '#f0f0f0',
  //         type: 'dashed',
  //       },
  //     },
  //   },
  //   yAxis: {
  //     type: 'value',
  //     name: '值',
  //     nameLocation: 'middle',
  //     nameGap: 50,
  //     nameTextStyle: {
  //       color: '#666',
  //       fontSize: 12,
  //     },
  //     axisLabel: {
  //       formatter: '{value}',
  //       color: '#666',
  //     },
  //     axisLine: {
  //       lineStyle: {
  //         color: '#ddd',
  //       },
  //     },
  //     splitLine: {
  //       show: true,
  //       lineStyle: {
  //         color: '#f0f0f0',
  //         type: 'dashed',
  //       },
  //     },
  //   },
  //   series: seriesData,
  //   backgroundColor: 'white',
  // }

  // chartInstance.setOption(option, true)
}

// 处理新的chartDataMock格式数据
const updateChartWithNewFormat = () => {
  const data = currentPerformanceData.value

  // 获取X轴配置信息
  const xAxisConfigs = Object.keys(data.x_axis)
  const isCompareMode = xAxisConfigs.length > 1

  // 构建图表系列数据
  const seriesData = []
  const legendData = []

  // 遍历每个系列
  data.series.forEach((seriesItem) => {
    // 遍历每个配置
    xAxisConfigs.forEach((configKey) => {
      const configData = seriesItem.configs[configKey]
      if (configData && configData.data) {
        const seriesName = isCompareMode ? `${seriesItem.name} (${configKey})` : seriesItem.name
        legendData.push(seriesName)

        // 获取对应的X轴数据
        const xAxisData = data.x_axis[configKey]

        // 构建数据点 [x, y]
        const chartData = configData.data.map((value, index) => [
          xAxisData[index] || index * 0.1, // 使用X轴数据或默认时间间隔
          value,
        ])

        seriesData.push({
          name: seriesName,
          type: 'line',
          data: chartData,
          smooth: true,
          symbol: 'circle',
          symbolSize: 4,
          lineStyle: {
            width: 2,
            type: configData.lineStyle || 'solid',
          },
          itemStyle: {
            color: configData.color || '#5470c6',
          },
          emphasis: {
            focus: 'series',
            lineStyle: {
              width: 3,
            },
          },
        })
      }
    })
  })

  // 构建图表配置
  const option = {
    title: {
      text: data.title || '性能指标对比',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        lineStyle: {
          color: '#999',
          width: 1,
          type: 'dashed',
        },
      },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#ddd',
      borderWidth: 1,
      borderRadius: 6,
      textStyle: {
        color: '#333',
        fontSize: 12,
      },
      formatter: function (params) {
        let result = `<div style="font-weight: bold; margin-bottom: 5px;">X轴: ${params[0].data[0].toFixed(3)}</div>`
        params.forEach((param) => {
          // 根据系列的线条类型创建不同的图标
          const series = seriesData.find((s) => s.name === param.seriesName)
          const lineType = series?.lineStyle?.type || 'solid'
          let iconStyle = ''

          if (lineType === 'dashed') {
            // 虚线图标
            iconStyle = `display: inline-block; width: 20px; height: 2px; background: linear-gradient(to right, ${param.color} 40%, transparent 40%, transparent 60%, ${param.color} 60%); margin-right: 5px; vertical-align: middle;`
          } else {
            // 实线图标
            iconStyle = `display: inline-block; width: 20px; height: 2px; background-color: ${param.color}; margin-right: 5px; vertical-align: middle;`
          }

          result += `<div style="margin: 2px 0;"><span style="${iconStyle}"></span>${param.seriesName}: ${param.data[1].toFixed(3)}</div>`
        })
        return result
      },
    },
    legend: {
      data: legendData.map((name, index) => {
        // 根据对应系列的线条类型设置图例图标
        const series = seriesData[index]
        const lineType = series?.lineStyle?.type || 'solid'
        return {
          name: name,
          icon:
            lineType === 'dashed'
              ? 'path://M0,10 L25,10 M0,10 L5,10 M10,10 L15,10 M20,10 L25,10'
              : 'path://M0,10 L25,10',
        }
      }),
      top: 40,
      type: 'scroll',
      orient: 'horizontal',
      textStyle: {
        fontSize: 12,
        color: '#666',
      },
      itemGap: 20,
      itemWidth: 25,
      itemHeight: 14,
    },
    grid: {
      left: 60,
      right: 40,
      top: 90,
      bottom: 60,
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      name: '时间(秒)',
      nameLocation: 'middle',
      nameGap: 30,
      nameTextStyle: {
        color: '#666',
        fontSize: 12,
      },
      axisLabel: {
        formatter: '{value}',
        color: '#666',
      },
      axisLine: {
        lineStyle: {
          color: '#ddd',
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#f0f0f0',
          type: 'dashed',
        },
      },
    },
    yAxis: {
      type: 'value',
      name: '值',
      nameLocation: 'middle',
      nameGap: 50,
      nameTextStyle: {
        color: '#666',
        fontSize: 12,
      },
      axisLabel: {
        formatter: '{value}',
        color: '#666',
      },
      axisLine: {
        lineStyle: {
          color: '#ddd',
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#f0f0f0',
          type: 'dashed',
        },
      },
    },
    series: seriesData,
    backgroundColor: 'white',
  }

  chartInstance.setOption(option, true)
}

// 更新表格显示
const updateTableDisplay = () => {
  if (
    !selectedMetric.value ||
    selectedConfigs.value.length === 0 ||
    selectedFields.value.length === 0
  ) {
    tableData.value = []
    return
  }

  if (Object.keys(currentPerformanceData.value).length === 0) {
    tableData.value = []
    return
  }

  // 生成统计数据
  const stats = []

  // 为每个配置和字段组合生成统计数据
  Object.entries(currentPerformanceData.value).forEach(([config, configData]) => {
    selectedFields.value.forEach((field) => {
      if (configData[field] && Array.isArray(configData[field])) {
        // 使用真实数据计算统计量
        const fieldStats = calculateStatistics({ [field]: configData[field] })

        if (fieldStats.length > 0) {
          const stat = fieldStats[0]
          stats.push({
            配置: config,
            字段: field,
            平均值: stat.mean,
            最小值: stat.min,
            最大值: stat.max,
            标准差: stat.stdDev,
          })
        }
      }
    })
  })

  // 添加特殊字段 [TOTAL] 和 [unaccounted]（如果数据中存在）
  Object.entries(currentPerformanceData.value).forEach(([config, configData]) => {
    // 检查是否有[TOTAL]字段
    if (configData['[TOTAL]'] && Array.isArray(configData['[TOTAL]'])) {
      const totalStats = calculateStatistics({ '[TOTAL]': configData['[TOTAL]'] })
      if (totalStats.length > 0) {
        const stat = totalStats[0]
        stats.unshift({
          配置: config,
          字段: '[TOTAL]',
          平均值: stat.mean,
          最小值: stat.min,
          最大值: stat.max,
          标准差: stat.stdDev,
        })
      }
    }

    // 检查是否有[unaccounted]字段
    if (configData['[unaccounted]'] && Array.isArray(configData['[unaccounted]'])) {
      const unaccountedStats = calculateStatistics({ '[unaccounted]': configData['[unaccounted]'] })
      if (unaccountedStats.length > 0) {
        const stat = unaccountedStats[0]
        stats.unshift({
          配置: config,
          字段: '[unaccounted]',
          平均值: stat.mean,
          最小值: stat.min,
          最大值: stat.max,
          标准差: stat.stdDev,
        })
      }
    }
  })

  // 为GPU指标添加All Selected Pass行
  if (selectedMetric.value.toLowerCase().includes('gpu')) {
    Object.keys(currentPerformanceData.value).forEach((config) => {
      // 计算该配置下所有选中字段的平均值总和（排除[TOTAL]和[unaccounted]）
      const configStats = stats.filter(
        (s) => s.配置 === config && s.字段 !== '[TOTAL]' && s.字段 !== '[unaccounted]',
      )

      if (configStats.length > 0) {
        const totalAvg = configStats.reduce((sum, s) => sum + s.平均值, 0)
        stats.push({
          配置: config,
          字段: 'All Selected Pass',
          平均值: parseFloat(totalAvg.toFixed(2)),
          最小值: 0,
          最大值: 0,
          标准差: 0,
        })
      }
    })
  }

  // 排序逻辑：优先级、配置、平均值
  stats.forEach((item) => {
    item.priority = ['[TOTAL]', '[unaccounted]'].includes(item.字段) ? 0 : 1
  })

  stats.sort((a, b) => {
    if (a.priority !== b.priority) return a.priority - b.priority
    if (a.配置 !== b.配置) return a.配置.localeCompare(b.配置)
    return b.平均值 - a.平均值
  })

  // 删除临时的priority字段
  stats.forEach((item) => delete item.priority)

  tableData.value = stats
}

// 初始化图表
const initChart = () => {
  if (chartContainer.value) {
    chartInstance = echarts.init(chartContainer.value)

    // 设置初始图表
    const option = {
      title: {
        text: '请选择数据进行分析',
        left: 'center',
        top: 'middle',
        textStyle: {
          color: '#999',
          fontSize: 16,
        },
      },
    }

    chartInstance.setOption(option)

    // 监听窗口大小变化
    window.addEventListener('resize', () => {
      chartInstance.resize()
    })
  }
}

// 监听数据变化，自动加载字段
watch(
  [selectedDate, selectedMetric],
  async () => {
    if (selectedDate.value && selectedMetric.value && selectedConfigs.value.length > 0) {
      await loadFieldsForMetric()
    }
  },
  { immediate: false },
)
// 组件挂载后初始化
onMounted(async () => {
  await initializeData()
  nextTick(() => {
    initChart()
  })
})
</script>

<style scoped lang="scss">
// SCSS Variables
$primary-bg: #f5f5f5;
$panel-bg: #f9f9f9;
$white-bg: #fff;
$border-color: #ddd;
$light-border: #eee;
$text-color: #333;
$hover-bg: #e6f7ff;
$shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
$border-radius: 4px;
$large-border-radius: 5px;

.dashboard-container {
  padding: 20px;
  background-color: $primary-bg;
  min-height: 100vh;
  margin-bottom: 50px;
}

.page-title {
  text-align: center;
  color: $text-color;
  margin-bottom: 30px;
  font-size: 28px;
  font-weight: 500;
}

.control-panel {
  background-color: $panel-bg;
  border: 1px solid $border-color;
  border-radius: $large-border-radius;
  padding: 20px;
  margin-bottom: 20px;
}

.control-row {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  //margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }

  @media (max-width: 768px) {
    flex-direction: column;
  }
}

.control-item {
  display: inline-block;
  vertical-align: top;
  margin: 10px;

  @media (max-width: 768px) {
    width: 100% !important;
    margin: 5px 0;
  }
}

.control-label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: $text-color;
}

.field-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding: 10px;
  background-color: $panel-bg;
  border-radius: $border-radius;
  border: 1px solid $light-border;
}

.field-title {
  font-weight: bold;
  color: $text-color;
}

.field-actions {
  display: flex;
  gap: 10px;
}

.field-toggle-container {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid $border-color;
  border-radius: $border-radius;
  padding: 10px;
  margin-top: 5px;
  background-color: $panel-bg;
}

.field-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: flex-start;
}

// 为el-checkbox-group添加样式，保持与field-grid相同的布局
:deep(.el-checkbox-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: flex-start;
  width: 100%;
}

.field-checkbox {
  padding: 8px 12px;
  margin: 2px;
  background-color: $primary-bg;
  border-radius: $border-radius;
  white-space: nowrap;
  min-width: fit-content;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-shrink: 0;
  line-height: 1.4;
  min-height: 32px;
  display: flex;
  align-items: center;

  &:hover {
    background-color: $hover-bg;
  }

  // 为长文本添加tooltip效果
  :deep(.el-checkbox__label) {
    max-width: 250px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    line-height: 1.4;
    padding-bottom: 2px; // 为下降字符预留空间
  }

  // 确保复选框本身也有足够的高度
  :deep(.el-checkbox) {
    height: auto;
    line-height: 1.4;
  }

  :deep(.el-checkbox__input) {
    line-height: 1.4;
  }
}

.update-section {
  text-align: center;
  margin-top: 10px;
}

.chart-section {
  margin-bottom: 20px;
}

.chart-container {
  width: 100%;
  height: 600px;
  background-color: $white-bg;
  border-radius: $border-radius;
  box-shadow: $shadow;
}

.table-section {
  background-color: $white-bg;
  border-radius: $border-radius;
  padding: 20px;
  box-shadow: $shadow;
}
</style>
