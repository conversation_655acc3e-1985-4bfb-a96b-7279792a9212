@use './base.scss';

#app {
    width: 100%;
    margin: 0 auto;
    padding: 0rem;
    font-weight: normal
}

@media (min-width: 1024px) {
    #app {
        padding: 0 0rem
    }
}

a,
.green {
    text-decoration: none;
    color: #00bd7e;
    transition: .4s;
    padding: 3px
}

@media (hover: hover) {

    a:hover,
    .green:hover {
        background-color: rgba(0, 189, 126, 0.2)
    }
}

@media (min-width: 1024px) {
    body {
        display: flex;
        place-items: center
    }
}