@use './base.scss';

// SCSS Variables
$app-max-width: 1280px;
$app-padding: 2rem;
$green-color: hsla(160, 100%, 37%, 1);
$green-hover-bg: hsla(160, 100%, 37%, 0.2);
$transition-duration: 0.4s;

#app {
  // max-width: $app-max-width;
  width: 100%;
  margin: 0 auto;
  // padding: $app-padding;
  font-weight: normal;

  @media (min-width: 1024px) {
    // padding: 0 $app-padding;
  }
}

a,
.green {
  text-decoration: none;
  color: $green-color;
  transition: $transition-duration;
  padding: 3px;

  @media (hover: hover) {
    &:hover {
      background-color: $green-hover-bg;
    }
  }
}

@media (min-width: 1024px) {
  body {
    display: flex;
    place-items: center;
  }
}
