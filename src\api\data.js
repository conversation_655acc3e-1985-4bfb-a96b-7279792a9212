// 数据API接口
const API_BASE_URL = 'http://localhost:8050' // 假设后端API地址

/**
 * 获取可用日期列表
 */
export async function getAvailableDates() {
  try {
    // 模拟从后端获取日期数据
    // 实际应该调用后端API: const response = await fetch(`${API_BASE_URL}/api/dates`)
    
    // 基于真实数据结构返回日期
    return [
      { label: '2025-08-29-DefaultTestCase', value: '2025-08-29-DefaultTestCase' }
    ]
  } catch (error) {
    console.error('获取日期失败:', error)
    return []
  }
}

/**
 * 获取指定日期下的配置列表
 */
export async function getAvailableConfigs(date) {
  try {
    // 模拟从后端获取配置数据
    // 实际应该调用: const response = await fetch(`${API_BASE_URL}/api/configs/${date}`)
    
    if (date === '2025-08-29-DefaultTestCase') {
      return [
        { label: '3060 (i7-10700)(2K)', value: '3060 (i7-10700)(2K)' }
      ]
    }
    return []
  } catch (error) {
    console.error('获取配置失败:', error)
    return []
  }
}

/**
 * 获取可用指标列表（基于JSON文件名）
 */
export async function getAvailableMetrics(date, config) {
  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 100))
    
    if (!date || !config) {
      return []
    }
    
    // 基于真实数据目录中的JSON文件名
    const realMetrics = [
      'AIBehaviorTree', 'AICrowd', 'AI_EQS', 'AbilitySystem', 'Anim', 'AnimationBudgetAllocator',
      'AsyncIO', 'AsyncLoadGameThread', 'Audio', 'AudioMixer', 'AudioThreadCommands', 'AutoShrinkContent',
      'BackChannel', 'CPUStalls', 'Canvas', 'Chaos', 'ChaosCollision', 'ChaosCollisionCounters',
      'ChaosConstraintSolver', 'ChaosCounters', 'ChaosEngine', 'ChaosIslands', 'ChaosJoint', 'ChaosNiagara',
      'Collision', 'CollisionTags', 'Component', 'Compression', 'Culling', 'D3D11RHI', 'D3D12Bindless',
      'D3D12BufferDetails', 'D3D12DescriptorHeap', 'D3D12MemoryDetails', 'D3D12PipelineState', 'D3D12RHI',
      'D3D12RHIPipeline', 'D3D12RayTracing', 'D3D12Resources', 'D3DMemory', 'DLSS', 'Engine', 'Foliage',
      'GC', 'GCloudModulesManager', 'GPAudio', 'GPU', 'GPUParticles', 'GPUSkinCache', 'Game', 'GameplayTags',
      'GeometryCache', 'HTTPThread', 'HttpReplay', 'ImgMediaPlugin', 'InitViews', 'InstanceData',
      'JadeZuesTOD', 'JadeZuesVolume', 'JadeZuesWeather', 'LLM', 'LLMFULL', 'LLMOverhead', 'LLMPlatform',
      'Landscape', 'LightRendering', 'LinkerCount', 'LiveLink', 'LoadTime', 'LogicDriver', 'Lumen',
      'MapBuildData', 'Media', 'Memory', 'MemoryAllocator', 'MemoryPlatform', 'MemoryStaticMesh',
      'MovieSceneECS', 'MovieSceneEval', 'Nanite', 'NaniteCoarseMeshStreaming', 'NaniteRayTracing',
      'NaniteStats', 'NaniteStreaming', 'Navigation', 'Net', 'Niagara', 'NiagaraDataChannels',
      'NiagaraOverview', 'NiagaraSystemCounts', 'Object', 'Online', 'OodleNetwork', 'OpenGLRHI',
      'PSCWorldMan', 'PSOPrecache', 'PVS', 'Packet', 'PakFile', 'ParticleMem', 'Particles',
      'ParticlesOverview', 'Physics', 'PhysicsFields', 'Ping', 'PipelineStateCache', 'PlayerController',
      'Quick', 'RDG', 'RHI', 'RHICMDLIST', 'RHITransientMemory', 'RayTracingGeometry', 'RenderTargetPool',
      'RenderThreadCommands', 'SFC', 'SceneCulling', 'SceneMemory', 'SceneQueryManager', 'SceneRendering',
      'SceneUpdate', 'ShaderCompiling', 'Shaders', 'ShadowRendering', 'SignificanceManager', 'Slate',
      'SlateMemory', 'StatSystem', 'Streaming', 'StreamingDetails', 'StreamingOverview', 'TODSequenceEval',
      'TaskGraphTasks', 'Text', 'TextureGroup', 'TexturePool', 'ThreadPoolAsyncTasks', 'Threading',
      'Threads', 'TickGroups', 'Tickables', 'Trace', 'UI', 'VSMStats', 'VTP', 'VirtualTextureMemory',
      'VirtualTexturing', 'Voice', 'VulkanBindless', 'VulkanMemory', 'VulkanMemoryRaw', 'VulkanPSO',
      'VulkanRHI', 'VulkanRayTracing', 'Water', 'WorldPartition', 'WwiseConcurrency', 'WwiseFileHandler',
      'WwiseFileHandlerLowLevelIO', 'WwiseMemory', 'WwiseResourceLoader', 'WwiseSoundEngine'
    ]
    
    return realMetrics.map(metric => ({
      label: metric,
      value: metric
    }))
  } catch (error) {
    console.error('获取指标失败:', error)
    return []
  }
}

/**
 * 获取指定指标的可用字段（基于JSON文件内的键名）
 */
export async function getAvailableFields(date, config, metric) {
  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 100))
    
    if (!date || !config || !metric) {
      return []
    }
    
    // 基于真实JSON文件内容的字段映射
    const fieldMappings = {
      'Engine': [
        'Static Mesh Tris', 'Static Mesh Draw Calls', 'Skeletal Mesh Tris', 'Skeletal Mesh Draw Calls',
        'FrameTime', 'GameThreadTime', 'RenderThreadTime', 'GPUTime', 'DynamicResolution',
        'Primitive Draw Calls', 'Mesh Draw Calls', 'Triangles Drawn', 'Vertices Drawn'
      ],
      'Memory': [
        'PageAllocator Free', 'PageAllocator Used', 'Physical Memory Used', 'Virtual Memory Used',
        'Memory Working Set', 'Memory Peak Working Set', 'Memory Page File', 'Memory Virtual',
        'Texture Memory', 'Render Target Memory', 'Audio Memory', 'Animation Memory'
      ],
      'GPU': [
        'RayTracingGeometry', 'Graphics0_Nanite', 'Graphics0_BasePass', 'Graphics0_Shadows',
        'Graphics0_Lights', 'Graphics0_Translucency', 'Graphics0_PostProcessing',
        'GPU Memory Used', 'GPU Memory Available', 'GPU Utilization', 'GPU Temperature',
        '[TOTAL]', '[unaccounted]'
      ],
      'Game': [
        'Game Thread Time', 'Tick Time', 'World Tick Time', 'Actor Count', 'Component Count',
        'Pawn Count', 'Controller Count', 'AI Count', 'Physics Bodies', 'Collision Queries'
      ],
      'SceneRendering': [
        'Draw Calls', 'Triangles', 'Vertices', 'Meshes', 'Lights', 'Shadows', 'Reflections',
        'Post Process', 'Translucency', 'Distortion', 'Velocity', 'Depth'
      ],
      'RHI': [
        'Draw Calls', 'Compute Dispatches', 'Triangles', 'Vertices', 'Texture Memory',
        'Buffer Memory', 'Render Targets', 'Depth Buffers', 'UAV Bindings', 'SRV Bindings'
      ],
      'Nanite': [
        'Triangles', 'Clusters', 'Nodes', 'Candidates', 'Visible Clusters', 'Occluded Clusters',
        'WPO Clusters', 'Streaming Requests', 'Root Pages', 'Cluster Pages'
      ],
      'Lumen': [
        'Scene Update Time', 'Lighting Update Time', 'Reflection Update Time', 'GI Update Time',
        'Surface Cache Updates', 'Radiance Cache Updates', 'Screen Probes', 'Final Gather Rays'
      ],
      'Threading': [
        'Worker Threads', 'Task Graph Tasks', 'Render Thread Time', 'Game Thread Time',
        'Audio Thread Time', 'Loading Thread Time', 'Streaming Thread Time', 'Physics Thread Time'
      ],
      'Physics': [
        'Rigid Bodies', 'Kinematic Bodies', 'Static Bodies', 'Constraints', 'Contacts',
        'Islands', 'Broadphase Pairs', 'Narrowphase Pairs', 'Simulation Time', 'Fetch Time'
      ]
    }
    
    // 获取指定指标的字段，如果没有映射则返回通用字段
    const fields = fieldMappings[metric] || [
      'Total', 'Average', 'Peak', 'Current', 'Delta', 'Count', 'Size', 'Time',
      '[TOTAL]', '[unaccounted]'
    ]
    
    return fields.map(field => ({
      label: field,
      value: field
    }))
  } catch (error) {
    console.error('获取字段失败:', error)
    return []
  }
}

/**
 * 获取性能数据（时间序列数据）
 */
export async function getPerformanceData(date, config, metric, fields) {
  try {
    // 模拟从后端获取性能数据
    // 实际应该调用: const response = await fetch(`${API_BASE_URL}/api/performance/${date}/${config}/${metric}`)
    
    if (!date || !config || !metric || !fields || fields.length === 0) {
      return {}
    }
    
    // 基于真实数据结构生成模拟数据
    const performanceData = {}
    
    // 生成时间序列数据，模拟真实JSON文件中的数组格式
    const timePoints = 300 // 模拟5分钟的数据，每秒一个点
    
    fields.forEach(field => {
      const data = []
      
      // 根据不同字段类型生成不同范围的数据
      let baseValue, variance
      
      switch (field) {
        case 'RayTracingGeometry':
          baseValue = 15000
          variance = 3000
          break
        case 'Graphics0_Nanite':
          baseValue = 8000
          variance = 1500
          break
        case 'Graphics0_BasePass':
          baseValue = 12000
          variance = 2000
          break
        case 'Graphics0_Shadows':
          baseValue = 5000
          variance = 1000
          break
        case 'Graphics0_Lights':
          baseValue = 3000
          variance = 500
          break
        case 'Graphics0_Translucency':
          baseValue = 2000
          variance = 400
          break
        case 'Graphics0_PostProcessing':
          baseValue = 4000
          variance = 800
          break
        case 'Static Mesh Tris':
          baseValue = 50000
          variance = 10000
          break
        case 'Static Mesh Draw Calls':
          baseValue = 200
          variance = 50
          break
        case 'FrameTime':
          baseValue = 16.67 // 60 FPS
          variance = 3
          break
        case 'GameThreadTime':
          baseValue = 8
          variance = 2
          break
        case 'RenderThreadTime':
          baseValue = 12
          variance = 3
          break
        case 'GPUTime':
          baseValue = 14
          variance = 4
          break
        case 'PageAllocator Free':
          baseValue = ********** // 2GB
          variance = *********
          break
        case 'PageAllocator Used':
          baseValue = ********** // 1GB
          variance = *********
          break
        case 'Physical Memory Used':
          baseValue = ********** // 8GB
          variance = **********
          break
        case '[TOTAL]':
          baseValue = 45000
          variance = 8000
          break
        case '[unaccounted]':
          baseValue = 2000
          variance = 500
          break
        default:
          baseValue = 1000
          variance = 200
      }
      
      // 生成时间序列数据点
      for (let i = 0; i < timePoints; i++) {
        const time = i // 时间（秒）
        const noise = (Math.random() - 0.5) * variance * 0.5
        const trend = Math.sin(i * 0.01) * variance * 0.3 // 添加周期性变化
        const value = Math.max(0, baseValue + noise + trend)
        
        data.push(value)
      }
      
      performanceData[field] = data
    })
    
    return performanceData
  } catch (error) {
    console.error('获取性能数据失败:', error)
    throw error
  }
}

/**
 * 获取统计数据（用于表格显示）
 */
export async function getStatisticsData(date, configs, metric, fields) {
  try {
    // 模拟从后端获取统计数据
    // 实际应该调用: const response = await fetch(`${API_BASE_URL}/api/statistics`, { method: 'POST', body: JSON.stringify({ date, configs, metric, fields }) })
    
    if (!date || !configs || configs.length === 0 || !metric || !fields || fields.length === 0) {
      return []
    }
    
    const statisticsData = []
    
    // 为每个配置和字段组合生成统计数据
    for (const config of configs) {
      for (const field of fields) {
        // 根据字段类型生成基准值
        let baseValue, variance
        
        switch (field) {
          case 'RayTracingGeometry':
            baseValue = 15000
            variance = 3000
            break
          case 'Graphics0_Nanite':
            baseValue = 8000
            variance = 1500
            break
          case 'Graphics0_BasePass':
            baseValue = 12000
            variance = 2000
            break
          case 'Graphics0_Shadows':
            baseValue = 5000
            variance = 1000
            break
          case 'Graphics0_Lights':
            baseValue = 3000
            variance = 500
            break
          case 'Graphics0_Translucency':
            baseValue = 2000
            variance = 400
            break
          case 'Graphics0_PostProcessing':
            baseValue = 4000
            variance = 800
            break
          case 'Static Mesh Tris':
            baseValue = 50000
            variance = 10000
            break
          case 'Static Mesh Draw Calls':
            baseValue = 200
            variance = 50
            break
          case 'FrameTime':
            baseValue = 16.67 // 60 FPS
            variance = 3
            break
          case 'GameThreadTime':
            baseValue = 8
            variance = 2
            break
          case 'RenderThreadTime':
            baseValue = 12
            variance = 3
            break
          case 'GPUTime':
            baseValue = 14
            variance = 4
            break
          case 'PageAllocator Free':
            baseValue = ********** // 2GB
            variance = *********
            break
          case 'PageAllocator Used':
            baseValue = ********** // 1GB
            variance = *********
            break
          case 'Physical Memory Used':
            baseValue = ********** // 8GB
            variance = **********
            break
          case '[TOTAL]':
            baseValue = 45000
            variance = 8000
            break
          case '[unaccounted]':
            baseValue = 2000
            variance = 500
            break
          default:
            baseValue = 1000
            variance = 200
        }
        
        // 生成统计值
        const avg = baseValue + (Math.random() - 0.5) * variance * 0.2
        const stdDev = variance * 0.1 + Math.random() * variance * 0.05
        const min = Math.max(0, avg - stdDev * 2)
        const max = avg + stdDev * 2
        const p50 = avg + (Math.random() - 0.5) * stdDev * 0.5
        const p95 = avg + stdDev * 1.5 + Math.random() * stdDev * 0.3
        const p99 = avg + stdDev * 1.8 + Math.random() * stdDev * 0.2
        
        statisticsData.push({
          config,
          field,
          min: Number(min.toFixed(2)),
          max: Number(max.toFixed(2)),
          avg: Number(avg.toFixed(2)),
          stdDev: Number(stdDev.toFixed(2)),
          p50: Number(p50.toFixed(2)),
          p95: Number(p95.toFixed(2)),
          p99: Number(p99.toFixed(2))
        })
      }
    }
    
    return statisticsData
  } catch (error) {
    console.error('获取统计数据失败:', error)
    throw error
  }
}

/**
 * 获取配置的元数据信息
 */
export async function getConfigMetadata(date, config) {
  try {
    // 模拟从后端获取元数据
    // 实际应该调用: const response = await fetch(`${API_BASE_URL}/api/metadata/${date}/${config}`)
    
    if (date === '2025-08-29-DefaultTestCase' && config === '3060 (i7-10700)(2K)') {
      return {
        cpu_brand: "Intel(R) Core(TM) i7-10700 CPU @ 2.90GHz",
        memory_capacity: "32",
        gpu_brand: "NVIDIA GeForce RTX 3060",
        gpu_memory_capacity: "12",
        render_resolution: "2K",
        TestCaseName: "shanzhuang-cloudy"
      }
    }
    
    return null
  } catch (error) {
    console.error('获取元数据失败:', error)
    return null
  }
}