export const chartDataMock = {
  x_axis: {
    '2025-08-29,3060 (i7-10700)(2K)': [0.016, 0.025, 0.038, 0.055],
    '2025-08-28,RTX4090 (i9-12900K)(4K)': [0.008, 0.015, 0.023, 0.031],
  },
  series: [
    {
      name: 'RayTracingGeometry',
      type: 'line',
      configs: {
        '2025-08-29,3060 (i7-10700)(2K)': {
          data: [0.1, 0.2, 0.15, 0.18],
          lineStyle: 'solid',
          color: '#5470c6',
        },
        '2025-08-28,RTX4090 (i9-12900K)(4K)': {
          data: [0.08, 0.18, 0.13, 0.16],
          lineStyle: 'dashed',
          color: '#5470c6',
        },
      },
    },
    {
      name: 'GPUSceneUpdate',
      type: 'line',
      configs: {
        '2025-08-29,3060 (i7-10700)(2K)': {
          data: [1.2, 1.5, 1.3, 1.4],
          lineStyle: 'solid',
          color: '#91cc75',
        },
        '2025-08-28,RTX4090 (i9-12900K)(4K)': {
          data: [0.9, 1.1, 1.0, 1.2],
          lineStyle: 'dashed',
          color: '#91cc75',
        },
      },
    },
    {
      name: 'Graphics0_BasePass',
      type: 'line',
      configs: {
        '2025-08-29,3060 (i7-10700)(2K)': {
          data: [2.1, 2.3, 2.0, 2.2],
          lineStyle: 'solid',
          color: '#fac858',
        },
        '2025-08-28,RTX4090 (i9-12900K)(4K)': {
          data: [1.8, 2.0, 1.9, 2.1],
          lineStyle: 'dashed',
          color: '#fac858',
        },
      },
    },
  ],
  title: 'GPU - 性能指标对比',
}
