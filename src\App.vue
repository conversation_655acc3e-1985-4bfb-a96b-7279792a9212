<script setup>
import { RouterView } from 'vue-router'
// import { ref } from 'vue'
// console.log('env', import.meta.env);

</script>

<template>
  <RouterView />
</template>

<style scoped lang="scss">
// header {
//   line-height: 1.5;
//   max-height: 100vh;

//   @media (min-width: 1024px) {
//     display: flex;
//     place-items: center;
//     padding-right: calc(var(--section-gap) / 2);

//     .wrapper {
//       display: flex;
//       place-items: flex-start;
//       flex-wrap: wrap;
//     }
//   }
// }

// .logo {
//   display: block;
//   margin: 0 auto 2rem;

//   @media (min-width: 1024px) {
//     margin: 0 2rem 0 0;
//   }
// }

// nav {
//   width: 100%;
//   font-size: 12px;
//   text-align: center;
//   margin-top: 2rem;

//   a {
//     display: inline-block;
//     padding: 0 1rem;
//     border-left: 1px solid var(--color-border);

//     &.router-link-exact-active {
//       color: var(--color-text);

//       &:hover {
//         background-color: transparent;
//       }
//     }

//     &:first-of-type {
//       border: 0;
//     }
//   }

//   @media (min-width: 1024px) {
//     text-align: left;
//     margin-left: -1rem;
//     font-size: 1rem;
//     padding: 1rem 0;
//     margin-top: 1rem;
//   }
// }
</style>
