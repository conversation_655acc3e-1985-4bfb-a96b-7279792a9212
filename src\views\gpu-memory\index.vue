<template>
  <div class="gpu-memory-page">
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" type="primary" plain class="back-button">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <h1>GPU内存分析</h1>
      </div>

      <div class="header-controls">
        <div class="control-group">
          <label for="dateSelect">选择日期:</label>
          <el-select
            v-model="selectedDate"
            placeholder="选择日期"
            @change="onDateChange"
            style="width: 200px"
          >
            <el-option v-for="date in availableDates" :key="date" :label="date" :value="date" />
          </el-select>
        </div>

        <div class="control-group">
          <label for="machineSelect">选择机型:</label>
          <el-select
            v-model="selectedMachine"
            placeholder="选择机型"
            @change="onMachineChange"
            style="width: 220px"
            :disabled="!selectedDate"
          >
            <el-option
              v-for="machine in availableMachines"
              :key="machine"
              :label="machine.label"
              :value="machine.value"
            />
          </el-select>
        </div>

        <div class="control-group">
          <label for="fileSelect">选择文件:</label>
          <el-select
            v-model="selectedFile"
            placeholder="选择文件"
            @change="onFileChange"
            style="width: 250px"
            :disabled="!selectedMachine"
          >
            <el-option v-for="file in availableFiles" :key="file" :label="file" :value="file" />
          </el-select>
        </div>
      </div>
    </div>

    <div class="chart-section">
      <div ref="chartContainer" class="chart-container"></div>
    </div>

    <div class="data-section" v-if="currentTableData.length > 0">
      <div class="table-header">
        <h3>{{ currentTableTitle }}</h3>
        <div class="table-controls">
          <el-input
            v-model="filterText"
            placeholder="筛选资源..."
            clearable
            style="width: 300px"
            @input="onFilterChange"
          />
          <el-button v-if="showAnalyzeButton" @click="analyzePoolAllocator" type="primary">
            分析内存池
          </el-button>
        </div>
      </div>

      <div class="table-stats">
        <span
          >找到 {{ filteredTableData.length }} 个资源，总内存: {{ totalMemory.toFixed(2) }} MB</span
        >
      </div>

      <el-table
        :data="paginatedTableData"
        style="width: 100%"
        max-height="600"
        @row-click="onRowClick"
        :row-class-name="getRowClassName"
      >
        <el-table-column prop="Name" label="名称" width="300" show-overflow-tooltip sortable />
        <el-table-column
          prop="Memory"
          label="内存 (MB)"
          width="120"
          sortable
          :sort-method="sortByMemory"
        >
          <template #default="scope">
            {{ formatMemory(scope.row.Memory) }}
          </template>
        </el-table-column>
        <el-table-column prop="Resolution" label="分辨率" width="120" />
        <el-table-column prop="Format" label="格式" width="150" show-overflow-tooltip />
        <el-table-column prop="Type" label="类型" width="120" sortable />
        <el-table-column prop="Owner" label="所有者" show-overflow-tooltip />
      </el-table>

      <div class="pagination-wrapper" v-if="filteredTableData.length > pageSize">
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="filteredTableData.length"
          layout="total, prev, pager, next, jumper"
          @current-change="onPageChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ArrowLeft } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import { useDashboardApi } from '@/api/dashboaard/index.js'
const { getDate, getMemoryFile, getDeviceConfig, getGPUMemoryData } = useDashboardApi()
const router = useRouter()
const route = useRoute()

// 响应式数据
const selectedDate = ref('')
const selectedMachine = ref('')
const selectedFile = ref('')
const availableDates = ref([])
const availableMachines = ref([])
const availableFiles = ref([])
const chartData = ref({})
const currentTableData = ref([])
const currentTableTitle = ref('')
const chartContainer = ref(null)
const filterText = ref('')
const currentPage = ref(1)
const pageSize = ref(500)
const showAnalyzeButton = ref(false)

// 数据存储
const memoryByType = ref({})
const listByType = ref({})
const heapGroupedItems = ref(new Map())
const allResourcesDataTable = ref(new Map())

let chartInstance = null
const dataRootDir = '/UE_benchmark/data/'

// 计算属性
const filteredTableData = computed(() => {
  if (!filterText.value || filterText.value.length <= 3) {
    return currentTableData.value
  }

  const filter = filterText.value.toLowerCase()
  return currentTableData.value.filter((item) => {
    return Object.values(item).some((value) => String(value).toLowerCase().includes(filter))
  })
})

const paginatedTableData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredTableData.value.slice(start, end)
})

const totalMemory = computed(() => {
  return filteredTableData.value.reduce((sum, item) => {
    const memory = parseFloat(item.Memory) || 0
    return sum + memory
  }, 0)
})

// 方法
const goBack = () => {
  router.go(-1)
}

const onFilterChange = () => {
  currentPage.value = 1
}

const onPageChange = (page) => {
  currentPage.value = page
}

const formatMemory = (memory) => {
  const num = parseFloat(memory)
  return isNaN(num) ? memory : num.toFixed(2)
}

const sortByMemory = (a, b) => {
  const memA = parseFloat(a.Memory) || 0
  const memB = parseFloat(b.Memory) || 0
  return memB - memA
}

// 动态加载数据的方法
const loadAvailableDates = async () => {
  try {
    const response = await getDate()
    if (response && response.data) {
      availableDates.value = response.data
    } else {
      availableDates.value = []
      ElMessage.warning('未获取到可用日期数据')
    }

    // 设置默认选中第一个日期
    if (availableDates.value.length > 0) {
      selectedDate.value = availableDates.value[0]
      await loadAvailableMachines()
    }
  } catch (error) {
    console.error('Failed to load available dates:', error)
    ElMessage.error('获取可用日期失败')
    availableDates.value = []
  }
}

const getRowClassName = ({ row }) => {
  if (row.Name && row.Name.includes('Heap')) {
    return 'heap-row'
  }
  return ''
}

const loadAvailableMachines = async () => {
  if (!selectedDate.value) return

  try {
    const response = await getDeviceConfig({ date: selectedDate.value })
    if (response.code === 200 && response.data) {
      availableMachines.value = response.data?.configs?.map((config) => ({
        label: config,
        value: config,
      }))
    } else {
      availableMachines.value = []
      ElMessage.warning('未获取到设备配置数据')
    }

    // 设置默认选中第一个机型
    if (availableMachines.value.length > 0) {
      selectedMachine.value = availableMachines.value[0].value
      await loadAvailableFiles()
    }
  } catch (error) {
    console.error('Failed to load available machines:', error)
    ElMessage.error('获取设备配置失败')
    availableMachines.value = []
  }
}

const loadAvailableFiles = async () => {
  if (!selectedDate.value || !selectedMachine.value) return

  try {
    const response = await getMemoryFile({
      date: selectedDate.value,
      config: selectedMachine.value,
    })
    if (response && response.data) {
      availableFiles.value = response.data
    } else {
      availableFiles.value = []
      ElMessage.warning('未获取到内存文件数据')
    }

    // 设置默认选中第一个文件
    if (availableFiles.value.length > 0) {
      selectedFile.value = availableFiles.value[0]
      await fetchData()
    }
  } catch (error) {
    console.error('Failed to load available files:', error)
    ElMessage.error('获取内存文件失败')
    availableFiles.value = []
  }
}

// 监听下拉选择变化
const onDateChange = async () => {
  selectedMachine.value = ''
  selectedFile.value = ''
  availableMachines.value = []
  availableFiles.value = []
  currentTableData.value = []

  if (selectedDate.value) {
    await loadAvailableMachines()
    updateURL()
  }
}

const onMachineChange = async () => {
  selectedFile.value = ''
  availableFiles.value = []
  currentTableData.value = []

  if (selectedMachine.value) {
    await loadAvailableFiles()
    updateURL()
  }
}

const onFileChange = async () => {
  if (selectedFile.value) {
    await fetchData()
    updateURL()
  }
}

// 更新URL参数
const updateURL = () => {
  const query = {}
  if (selectedDate.value) query.date = selectedDate.value
  if (selectedMachine.value) query.machine = selectedMachine.value
  router.replace({ query })
}

// 表格行点击事件
const onRowClick = async (row) => {
  if (row.Name && row.Name.includes('Heap')) {
    // 点击Heap行，重新分析该Heap的数据
    const heapName = row.Name
    if (heapGroupedItems.value.has(heapName)) {
      const heapData = heapGroupedItems.value.get(heapName)
      currentTableData.value = heapData.sort(sortByMemory)
      currentTableTitle.value = `${heapName} 详细资源`
      currentPage.value = 1
      filterText.value = ''
      showAnalyzeButton.value = false
    }
  } else if (row.Type === 'PoolAllocator') {
    // 显示分析按钮
    showAnalyzeButton.value = true
  }
}

// 分析内存池
const analyzePoolAllocator = async () => {
  try {
    // 获取当前选择的文件路径
    const filePath = `${dataRootDir}${selectedDate.value}/${selectedMachine.value}/${selectedFile.value}`

    // 重新获取并分析数据
    await fetchData()

    ElMessage.success('内存池分析完成')
  } catch (error) {
    console.error('Failed to analyze pool allocator:', error)
    ElMessage.error('内存池分析失败')
  }
}

// 数据获取函数 - 使用固定模拟数据
const fetchData = async () => {
  try {
    if (!selectedDate.value || !selectedMachine.value || !selectedFile.value) {
      ElMessage.warning('请先选择日期、机型和文件')
      return
    }

    // 使用真实API接口获取GPU内存数据
    const response = await getGPUMemoryData({
      date: selectedDate.value,
      machine: selectedMachine.value,
      file: selectedFile.value,
    })

    if (response && response.data) {
      const gpuMemoryData = response.data

      // 将API返回的数据转换为表格需要的格式
      const tableData = []

      if (gpuMemoryData.resources && Array.isArray(gpuMemoryData.resources)) {
        gpuMemoryData.resources.forEach((resource) => {
          tableData.push({
            Name: resource.name || resource.Name || 'Unknown',
            Memory: resource.memory || resource.Memory || '0',
            Type: resource.type || resource.Type || 'Unknown',
            Size: resource.size || resource.Size || 'Unknown',
            Format: resource.format || resource.Format || 'Unknown',
            Resolution: resource.resolution || resource.Resolution || 'Unknown',
            Owner: resource.owner || resource.Owner || 'Unknown',
          })
        })
      }

      currentTableData.value = tableData.sort(sortByMemory)
      currentTableTitle.value = `GPU内存资源分析 - ${selectedFile.value}`

      // 按类型分组数据用于图表显示
      const groupedData = {}
      tableData.forEach((item) => {
        const type = item.Type
        if (!groupedData[type]) {
          groupedData[type] = []
        }
        groupedData[type].push(item)
      })

      // 计算每种类型的总内存
      const chartLabels = []
      const chartSizes = []

      Object.keys(groupedData).forEach((type) => {
        const totalMemory = groupedData[type].reduce((sum, item) => {
          return sum + parseFloat(item.Memory)
        }, 0)
        chartLabels.push(type)
        chartSizes.push(totalMemory)
      })

      // 存储分组数据供图表点击使用
      listByType.value = groupedData

      // 更新图表
      updateChart(chartLabels, chartSizes)

      ElMessage.success('数据加载完成')
    } else {
      ElMessage.error('未获取到有效的GPU内存数据')
    }
  } catch (error) {
    console.error('Failed to fetch GPU memory data:', error)
    ElMessage.error('获取GPU内存数据失败: ' + (error.message || '未知错误'))
  }
}

const displayDataList = (type, data) => {
  currentTableTitle.value = `${type} (${data.length} 个资源)`
  currentTableData.value = Array.isArray(data) ? data : []
  showAnalyzeButton.value = type.includes('PoolAllocator')
  currentPage.value = 1
  filterText.value = ''
}

const updateChart = (labels, sizes) => {
  if (!chartContainer.value || !labels.length) return

  if (chartInstance) {
    chartInstance.dispose()
  }

  chartInstance = echarts.init(chartContainer.value)

  const totalMemory = sizes.reduce((total, size) => total + size, 0)

  const option = {
    title: {
      text: `GPU内存分析 - 总计: ${totalMemory.toFixed(2)} MB`,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: function (params) {
        const data = params[0]
        return `${data.name}<br/>${data.seriesName}: ${data.value.toFixed(2)} MB`
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: labels,
      axisLabel: {
        rotate: 45,
        interval: 0,
        fontSize: 10,
      },
    },
    yAxis: {
      type: 'value',
      name: '内存使用量 (MB)',
      nameTextStyle: {
        fontSize: 12,
      },
      axisLabel: {
        formatter: '{value} MB',
      },
    },
    series: [
      {
        name: 'GPU内存使用',
        type: 'bar',
        data: sizes.map((value, index) => ({
          value: value,
          name: labels[index],
          itemStyle: {
            color: generateColor(index),
          },
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  }

  chartInstance.setOption(option)

  // 添加点击事件
  chartInstance.off('click')
  chartInstance.on('click', function (params) {
    const clickedName = params.name
    const data = listByType.value[clickedName]
    if (data) {
      displayDataList(clickedName, data)
    }
  })

  // 监听窗口大小变化
  const resizeHandler = () => {
    if (chartInstance) {
      chartInstance.resize()
    }
  }
  window.addEventListener('resize', resizeHandler)

  chartData.value = { labels, data: sizes }
}

// 生成颜色的函数
const generateColor = (index) => {
  const colors = [
    'rgba(54, 162, 235, 0.6)',
    'rgba(255, 99, 132, 0.6)',
    'rgba(75, 192, 192, 0.6)',
    'rgba(255, 206, 86, 0.6)',
    'rgba(153, 102, 255, 0.6)',
    'rgba(255, 159, 64, 0.6)',
    'rgba(199, 199, 199, 0.6)',
    'rgba(83, 202, 115, 0.6)',
    'rgba(192, 192, 192, 0.6)',
    'rgba(155, 89, 182, 0.6)',
    'rgba(255, 215, 0, 0.6)',
    'rgba(144, 238, 144, 0.6)',
    'rgba(255, 127, 80, 0.6)',
    'rgba(189, 189, 189, 0.6)',
    'rgba(242, 242, 242, 0.6)',
    'rgba(127, 127, 127, 0.6)',
    'rgba(0, 0, 0, 0.6)',
    'rgba(255, 255, 255, 0.6)',
  ]
  return colors[index % colors.length]
}

// 组件挂载时初始化
onMounted(async () => {
  await loadAvailableDates()
  // 确保在DOM渲染完成后初始化图表
  await nextTick()
  await fetchData()
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', () => {
    if (chartInstance) {
      chartInstance.resize()
    }
  })
})
</script>

<style scoped lang="scss">
.gpu-memory-page {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;

  .header-left {
    display: flex;
    align-items: center;
    gap: 15px;

    .back-button {
      margin-right: 5px;
    }

    h1 {
      margin: 0;
      color: #333;
      font-size: 24px;
    }
  }

  .header-controls {
    display: flex;
    gap: 24px;
    flex-wrap: wrap;
    align-items: center;

    .control-group {
      display: flex;
      align-items: center;
      gap: 12px;
      min-width: auto;

      label {
        font-weight: 500;
        color: #666;
        font-size: 14px;
        white-space: nowrap;
        margin: 0;
      }

      .el-select {
        min-width: 160px;
      }
    }
  }
}

.chart-section {
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .chart-container {
    width: 100%;
    height: 500px;
  }

  .empty-chart {
    width: 100%;
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.data-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;

  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    h3 {
      margin: 0;
      color: #333;
      font-size: 18px;
      font-weight: 600;
    }

    .table-controls {
      display: flex;
      gap: 15px;
      align-items: center;
    }
  }

  .table-stats {
    margin-bottom: 15px;
    color: #666;
    font-size: 14px;
  }

  .pagination-wrapper {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }

  :deep(.heap-row) {
    background-color: #f0f9ff;
    cursor: pointer;

    &:hover {
      background-color: #e0f2fe;
    }
  }

  :deep(.el-table__row) {
    &:hover {
      background-color: #f5f5f5;
    }
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;

    .header-left {
      width: 100%;
      justify-content: flex-start;
    }

    .header-controls {
      width: 100%;
      flex-direction: column;
      gap: 15px;
      align-items: stretch;

      .control-group {
        min-width: auto;
        flex-direction: row;
        justify-content: space-between;

        label {
          flex-shrink: 0;
        }

        .el-select {
          flex: 1;
          min-width: 120px;
          max-width: 200px;
        }
      }
    }
  }

  .chart-section {
    .chart-container {
      height: 400px;
    }

    .empty-chart {
      height: 300px;
    }
  }
}
</style>
