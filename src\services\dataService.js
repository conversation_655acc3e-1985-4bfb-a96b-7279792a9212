/**
 * 数据服务 - 处理性能数据的获取和解析
 * 基于UE_benchmark的数据结构和逻辑
 */

import axios from 'axios'

// 数据文件路径配置
const DATA_CONFIG = {
  // 本地数据路径（开发环境）
  LOCAL_DATA_PATH: '/UE_benchmark/data',
  // API基础URL（生产环境）
  API_BASE_URL: '/api/benchmark',
  // 是否使用本地数据
  USE_LOCAL_DATA: true
}

/**
 * 获取可用日期列表
 * 对应UE_benchmark中的get_available_dates函数
 */
export const getAvailableDates = async () => {
  try {
    if (DATA_CONFIG.USE_LOCAL_DATA) {
      // 模拟从本地数据目录获取日期
      // 实际实现中需要读取data目录下的文件夹名称
      return [
        { label: '2025-08-29-DefaultTestCase', value: '2025-08-29-DefaultTestCase' },
        { label: '2025-08-28-TestCase1', value: '2025-08-28-TestCase1' },
        { label: '2025-08-27-TestCase2', value: '2025-08-27-TestCase2' }
      ]
    } else {
      // API调用
      const response = await axios.get(`${DATA_CONFIG.API_BASE_URL}/dates`)
      return response.data.map(date => ({
        label: date,
        value: date
      }))
    }
  } catch (error) {
    console.error('获取日期列表失败:', error)
    return []
  }
}

/**
 * 获取指定日期的可用配置列表
 * 对应UE_benchmark中的get_available_configs函数
 */
export const getAvailableConfigs = async (date) => {
  try {
    if (DATA_CONFIG.USE_LOCAL_DATA) {
      // 模拟从本地数据获取配置
      return [
        { label: '3060 (i7-10700)(2K)', value: '3060 (i7-10700)(2K)' },
        { label: 'RTX 4090 (Intel i9-13900K)(4K)', value: 'RTX 4090 (Intel i9-13900K)(4K)' },
        { label: 'RTX 4080 (AMD Ryzen 9 7950X)(2K)', value: 'RTX 4080 (AMD Ryzen 9 7950X)(2K)' }
      ]
    } else {
      // API调用
      const response = await axios.get(`${DATA_CONFIG.API_BASE_URL}/configs`, {
        params: { date }
      })
      return response.data.map(config => ({
        label: config,
        value: config
      }))
    }
  } catch (error) {
    console.error('获取配置列表失败:', error)
    return []
  }
}

/**
 * 获取可用的性能指标列表
 * 对应UE_benchmark中的性能指标JSON文件名
 */
export const getAvailableMetrics = async () => {
  try {
    if (DATA_CONFIG.USE_LOCAL_DATA) {
      // 基于UE_benchmark中发现的JSON文件名
      return [
        { label: 'GPU', value: 'GPU' },
        { label: 'EngineFrameTime', value: 'EngineFrameTime' },
        { label: 'GameThreadTime', value: 'GameThreadTime' },
        { label: 'RenderThreadTime', value: 'RenderThreadTime' },
        { label: 'Streaming', value: 'Streaming' },
        { label: 'Memory', value: 'Memory' },
        { label: 'Shaders', value: 'Shaders' },
        { label: 'VulkanRHI', value: 'VulkanRHI' },
        { label: 'Threading', value: 'Threading' },
        { label: 'Slate', value: 'Slate' },
        { label: 'Audio', value: 'Audio' },
        { label: 'Physics', value: 'Physics' },
        { label: 'Animation', value: 'Animation' }
      ]
    } else {
      // API调用
      const response = await axios.get(`${DATA_CONFIG.API_BASE_URL}/metrics`)
      return response.data.map(metric => ({
        label: metric,
        value: metric
      }))
    }
  } catch (error) {
    console.error('获取指标列表失败:', error)
    return []
  }
}

/**
 * 获取指定指标的可用字段列表
 * 对应UE_benchmark中的get_json_fields函数
 */
export const getAvailableFields = async (date, config, metric) => {
  try {
    if (DATA_CONFIG.USE_LOCAL_DATA) {
      // 模拟从JSON文件获取字段
      // 基于UE_benchmark中发现的字段名称
      const commonFields = [
        'RayTracingGeometry', 'VulkanBindless', 'WorldPartition', 'VTP', 
        'VirtualTexturing', 'Water', 'TODSequenceEval', 'WwiseMemory',
        'VulkanRHI', 'TextureGroup', 'StreamingOverview', 'Slate',
        'Streaming', 'WwiseSoundEngine', 'VirtualTextureMemory', 'VulkanPSO',
        'Trace', 'Text', 'Shaders', 'VulkanRayTracing', 'Threads',
        'TickGroups', 'SignificanceManager', 'StreamingDetails', 'StatSystem',
        'UI', 'VSMStats', 'VulkanMemoryRaw', 'ThreadPoolAsyncTasks',
        'Threading', 'SlateMemory', 'Voice', 'VulkanMemory', 'SFC',
        'TexturePool', 'WwiseResourceLoader', 'ShadowRendering', 'Tickables',
        'TaskGraphTasks', 'WwiseFileHandlerLowLevelIO', 'WwiseFileHandler',
        'WwiseConcurrency'
      ]
      
      // 根据不同指标返回不同的字段子集
      if (metric === 'GPU') {
        return ['RayTracingGeometry', 'VulkanRHI', 'VulkanPSO', 'VulkanRayTracing', 'VulkanMemory', 'VulkanMemoryRaw']
      } else if (metric === 'Memory') {
        return ['WwiseMemory', 'VirtualTextureMemory', 'VulkanMemoryRaw', 'SlateMemory', 'VulkanMemory']
      } else if (metric === 'Threading') {
        return ['Threads', 'Threading', 'ThreadPoolAsyncTasks', 'TaskGraphTasks']
      } else {
        return commonFields.slice(0, 10) // 返回前10个字段作为示例
      }
    } else {
      // API调用
      const response = await axios.get(`${DATA_CONFIG.API_BASE_URL}/fields`, {
        params: { date, config, metric }
      })
      return response.data
    }
  } catch (error) {
    console.error('获取字段列表失败:', error)
    return []
  }
}

/**
 * 获取性能数据
 * 对应UE_benchmark中的load_data函数
 */
export const getPerformanceData = async (date, config, metric, fields) => {
  try {
    if (DATA_CONFIG.USE_LOCAL_DATA) {
      // 模拟从JSON文件加载数据
      // 生成模拟的时间序列数据
      const data = {}
      
      fields.forEach(field => {
        // 生成100个数据点的时间序列
        const timeSeriesData = Array.from({ length: 100 }, (_, i) => {
          const timestamp = i * 0.1 // 每0.1秒一个数据点
          const baseValue = Math.random() * 50 + 10
          const noise = (Math.random() - 0.5) * 5
          return timestamp * 1000 + baseValue + noise // 转换为毫秒
        })
        
        data[field] = timeSeriesData
      })
      
      return data
    } else {
      // API调用
      const response = await axios.post(`${DATA_CONFIG.API_BASE_URL}/data`, {
        date,
        config,
        metric,
        fields
      })
      return response.data
    }
  } catch (error) {
    console.error('获取性能数据失败:', error)
    return {}
  }
}

/**
 * 计算统计数据
 * 对应UE_benchmark中的统计计算逻辑
 */
export const calculateStatistics = (data) => {
  const statistics = []
  
  Object.entries(data).forEach(([field, values]) => {
    if (Array.isArray(values) && values.length > 0) {
      // 计算基本统计量
      const sortedValues = [...values].sort((a, b) => a - b)
      const mean = values.reduce((sum, val) => sum + val, 0) / values.length
      const min = Math.min(...values)
      const max = Math.max(...values)
      
      // 计算标准差
      const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
      const stdDev = Math.sqrt(variance)
      
      // 计算百分位数
      const getPercentile = (arr, percentile) => {
        const index = Math.ceil((percentile / 100) * arr.length) - 1
        return arr[Math.max(0, index)]
      }
      
      const p50 = getPercentile(sortedValues, 50)
      const p90 = getPercentile(sortedValues, 90)
      const p95 = getPercentile(sortedValues, 95)
      const p99 = getPercentile(sortedValues, 99)
      
      statistics.push({
        field,
        mean: parseFloat(mean.toFixed(2)),
        min: parseFloat(min.toFixed(2)),
        max: parseFloat(max.toFixed(2)),
        stdDev: parseFloat(stdDev.toFixed(2)),
        p50: parseFloat(p50.toFixed(2)),
        p90: parseFloat(p90.toFixed(2)),
        p95: parseFloat(p95.toFixed(2)),
        p99: parseFloat(p99.toFixed(2)),
        count: values.length
      })
    }
  })
  
  return statistics
}

/**
 * 获取GPU内存数据
 * 对应UE_benchmark中的GPU内存分析功能
 */
export const getGPUMemoryData = async (date, config) => {
  try {
    if (DATA_CONFIG.USE_LOCAL_DATA) {
      // 模拟GPU内存数据，返回更详细的结构
      return {
        startMemory: 2048 * 1024 * 1024, // 2GB in bytes
        endMemory: 3072 * 1024 * 1024,   // 3GB in bytes
        startBreakdown: {
          'RayTracingGeometry': 512 * 1024 * 1024,
          'TexturePool': 768 * 1024 * 1024,
          'VulkanMemory': 256 * 1024 * 1024,
          'Shaders': 128 * 1024 * 1024,
          'Other': 384 * 1024 * 1024
        },
        endBreakdown: {
          'RayTracingGeometry': 768 * 1024 * 1024,
          'TexturePool': 1024 * 1024 * 1024,
          'VulkanMemory': 512 * 1024 * 1024,
          'Shaders': 256 * 1024 * 1024,
          'Other': 512 * 1024 * 1024
        }
      }
    } else {
      // API调用
      const response = await axios.get(`${DATA_CONFIG.API_BASE_URL}/gpu-memory`, {
        params: { date, config }
      })
      return response.data
    }
  } catch (error) {
    console.error('获取GPU内存数据失败:', error)
    return null
  }
}

/**
 * 上传性能数据文件
 * 对应UE_benchmark中的/Upload端点
 */
export const uploadPerformanceFiles = async (files) => {
  try {
    const formData = new FormData()
    files.forEach((file, index) => {
      formData.append(`file${index}`, file)
    })
    
    if (DATA_CONFIG.USE_LOCAL_DATA) {
      // 模拟上传成功
      return {
        success: true,
        message: '文件上传成功',
        processedFiles: files.map(f => f.name)
      }
    } else {
      // API调用
      const response = await axios.post(`${DATA_CONFIG.API_BASE_URL}/upload`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      return response.data
    }
  } catch (error) {
    console.error('上传文件失败:', error)
    throw error
  }
}

// 导出配置，供其他组件使用
export { DATA_CONFIG }